{"name": "simbios", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "typecheck": "tsc -b", "lint": "eslint src/ --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src/ --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --ignore-unknown --check .", "format:fix": "prettier --ignore-unknown --write .", "prepare": "cd .. && husky frontend/.husky", "storybook": "storybook dev -p 6006 --no-open", "build-storybook": "storybook build"}, "dependencies": {"@dagrejs/dagre": "^1.1.5", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@sentry/react": "^10.5.0", "@tailwindcss/vite": "^4.1.12", "@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "^5.85.5", "@tanstack/react-router": "^1.131.27", "@tanstack/react-router-devtools": "^1.131.27", "@tanstack/router-plugin": "^1.131.27", "@xyflow/react": "^12.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-day-picker": "^9.9.0", "react-dom": "^19.1.1", "recharts": "2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "zustand": "^5.0.8"}, "devDependencies": {"@eslint-react/eslint-plugin": "^1.53.1", "@eslint/js": "^9.35.0", "@iconify-json/ic": "^1.2.4", "@storybook/addon-a11y": "^9.1.6", "@storybook/addon-docs": "^9.1.6", "@storybook/addon-vitest": "^9.1.6", "@storybook/react-vite": "^9.1.6", "@svgr/core": "^8.1.0", "@svgr/plugin-jsx": "^8.1.0", "@tanstack/eslint-plugin-query": "^5.89.0", "@tanstack/eslint-plugin-router": "^1.131.2", "@types/d3-hierarchy": "^3.1.7", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.3", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.35.0", "eslint-config-prettier": "^10.1.8", "eslint-formatter-gitlab": "^6.0.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-unicorn": "^61.0.2", "globals": "^16.4.0", "husky": "^9.1.7", "lint-staged": "^16.1.6", "playwright": "^1.55.0", "prettier": "^3.6.2", "prettier-plugin-multiline-arrays": "^4.0.3", "storybook": "^9.1.6", "tailwind-scrollbar": "^4.0.2", "tw-animate-css": "^1.3.8", "typescript": "~5.9.2", "typescript-eslint": "^8.44.0", "unplugin-icons": "^22.3.0", "vite": "^7.1.5", "vitest": "^3.2.4"}, "lint-staged": {"!*.{js,jsx,ts,tsx}": "prettier --ignore-unknown --write", "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}