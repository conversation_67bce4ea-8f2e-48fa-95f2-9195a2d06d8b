{"lsp": {"tailwindcss-language-server": {"settings": {"classFunctions": ["cva", "cx", "cn", "clsx"]}}}, "languages": {"TSX": {"formatter": {"external": {"command": "prettier", "arguments": ["--stdin-filepath", "{buffer_path}"]}}, "code_actions_on_format": {"source.fixAll.eslint": true}}, "TypeScript": {"formatter": {"external": {"command": "prettier", "arguments": ["--stdin-filepath", "{buffer_path}"]}}, "code_actions_on_format": {"source.fixAll.eslint": true}}, "JavaScript": {"formatter": {"external": {"command": "prettier", "arguments": ["--stdin-filepath", "{buffer_path}"]}}, "code_actions_on_format": {"source.fixAll.eslint": true}}, "CSS": {"formatter": {"external": {"command": "prettier", "arguments": ["--stdin-filepath", "{buffer_path}"]}}}, "JSON": {"formatter": {"external": {"command": "prettier", "arguments": ["--stdin-filepath", "{buffer_path}"]}}}}}