import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'
import prettier from 'eslint-config-prettier/flat'
import eslintReact from '@eslint-react/eslint-plugin'
import eslint<PERSON>luginUnicorn from 'eslint-plugin-unicorn'
import pluginRouter from '@tanstack/eslint-plugin-router'
import pluginQuery from '@tanstack/eslint-plugin-query'
import { globalIgnores } from 'eslint/config'

export default tseslint.config([
    globalIgnores(['dist', '**/*.gen.ts']),
    {
        files: ['**/*.{ts,tsx}'],
        extends: [
            js.configs.recommended,
            tseslint.configs.recommendedTypeChecked,
            tseslint.configs.stylisticTypeChecked,
            eslintReact.configs['recommended-typescript'],
            reactHooks.configs['recommended-latest'],
            eslintPluginUnicorn.configs.recommended,
            ...pluginRouter.configs['flat/recommended'],
            ...pluginQuery.configs['flat/recommended'],
            reactRefresh.configs.vite,
            prettier,
        ],
        languageOptions: {
            ecmaVersion: 2020,
            globals: globals.browser,
            parser: tseslint.parser,
            parserOptions: {
                projectService: true,
                tsconfigRootDir: import.meta.dirname,
            },
        },
        settings: {
            react: {
                version: 'detect',
            },
        },
        rules: {
            'no-console': 'warn',

            'unicorn/prevent-abbreviations': 'off',
            'unicorn/no-null': 'off',

            'react-refresh/only-export-components': 'off',
        },
    },
])
