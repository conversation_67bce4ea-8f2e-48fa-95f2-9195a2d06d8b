import { useCallback, useState } from 'react'
import { type ReactFlowInstance, type Edge } from '@xyflow/react'
import type { EmployeeNode } from '../models/types'

interface UseViewportManagementOptions {
    /**
     * Отступы от ноды при фокусе
     */
    padding?: number
    /**
     * Процент отступа от центра экрана
     * @default 0.75 (75% от центра)
     */
    rootNodePositionRatio?: number
}

interface ViewportDimensions {
    width: number
    height: number
}

export function useViewportManagement(options: UseViewportManagementOptions) {
    const { padding = 5, rootNodePositionRatio = 0.7 } = options
    const [isViewportLoading, setIsViewportLoading] = useState(true)

    const getContainerDimensions = useCallback((): ViewportDimensions => {
        const reactFlowWrapper = document.querySelector('.react-flow')
        if (reactFlowWrapper) {
            const rect = reactFlowWrapper.getBoundingClientRect()
            return {
                width: rect.width,
                height: rect.height,
            }
        }

        return {
            width: window.innerWidth,
            height: window.innerHeight,
        }
    }, [])

    /**
     * Центрирует viewport на корневой ноде, с нужным оступом и паддингом
     * Должен вызываться только после инициализации React Flow (onInit)
     */
    const focusOnRootNode = useCallback(
        async (reactFlowInstance: ReactFlowInstance<EmployeeNode, Edge>) => {
            try {
                // Получаем текущие размеры канваса
                const currentDimensions = getContainerDimensions()

                const rootNode = reactFlowInstance.getNodes()[0]

                // Центрирование вьюпорта на корневой ноде
                await reactFlowInstance.fitView({
                    nodes: [rootNode],
                    padding,
                })

                const viewport = reactFlowInstance.getViewport()

                // Вычисляется отступ сверху, в зависимости от размера вьюпорта и коэффициента
                const verticalOffset = (currentDimensions.height * rootNodePositionRatio * -1) / 2

                await reactFlowInstance.setViewport({
                    x: viewport.x,
                    y: viewport.y + verticalOffset,
                    zoom: viewport.zoom,
                })
                setIsViewportLoading(false)
            } catch (error) {
                if (process.env.NODE_ENV === 'development') {
                    // eslint-disable-next-line no-console
                    console.error('Failed to initialize viewport:', error)
                }
            }
        },
        [
            getContainerDimensions,
            padding,
            rootNodePositionRatio,
        ],
    )

    return {
        /**
         * Центрирует viewport на корневой ноде, с нужным оступом и паддингом
         * Должен вызываться только после инициализации React Flow (onInit)
         */
        focusOnRootNode,
        /**
         * Флаг, который показывает, что вьюпорт еще не рассчитан.
         */
        isViewportLoading,
    }
}
