import type { ForwardRefExoticComponent, SVGProps } from 'react'
import OutlineGraphIcon from '~icons/custom-icons/outline-graph'
import IcOutlineAccountBalanceWallet from '~icons/ic/outline-account-balance-wallet'
import IcOutlineCalendarMonth from '~icons/ic/outline-calendar-month'
import IcOutlineDynamicFeed from '~icons/ic/outline-dynamic-feed'
import IcOutlineFolderOpen from '~icons/ic/outline-folder-open'
import IcOutlineGridView from '~icons/ic/outline-grid-view'
import IcOutlinePieChartOutline from '~icons/ic/outline-pie-chart-outline'
import IcOutlineSettings from '~icons/ic/outline-settings'
import IcOutlineSupervisorAccount from '~icons/ic/outline-supervisor-account'
import IcOutlineViewDay from '~icons/ic/outline-view-day'
import IcOutlineViewTimeline from '~icons/ic/outline-view-timeline'
import { SIDEBAR_ICON_SIZE } from './sidebar'
import { SidebarGroup, SidebarGroupLabel } from './ui/kit/sidebar-group'
import { SidebarMenu, SidebarMenuLink } from './ui/kit/sidebar-menu'
import { Separator } from './ui/kit/sidebar-separator'

interface MenuItem {
    href: string
    label: string
    icon: ForwardRefExoticComponent<SVGProps<SVGSVGElement>>
}

interface MenuGroup {
    title: string
    items: MenuItem[]
}

const menuData: MenuGroup[] = [
    {
        title: 'Основное',
        items: [
            { href: '/', label: 'Рабочий стол', icon: IcOutlineGridView },
            // TODO PLACEHOLDER поменять на реальный эндпоинт
            { href: '/test', label: '!Тест!', icon: IcOutlineCalendarMonth },
        ],
    },
    {
        title: 'Планирование',
        items: [
            { href: '#', label: 'Календарный план', icon: IcOutlineViewTimeline },
            { href: '/objects', label: 'Сетевой график', icon: OutlineGraphIcon },
            { href: '/processes', label: 'Бюджет', icon: IcOutlineAccountBalanceWallet },
        ],
    },
    {
        title: 'Ресурсы',
        items: [
            { href: '#', label: 'Команда', icon: IcOutlineSupervisorAccount },
            { href: '/company', label: 'Структура компании', icon: IcOutlineDynamicFeed },
            { href: '/processes', label: 'Матрица ответственности', icon: IcOutlineViewDay },
        ],
    },
    {
        title: 'Аналитика',
        items: [
            { href: '#', label: 'Отчёты и метрики', icon: IcOutlinePieChartOutline },
            { href: '/objects', label: 'Паспорт проекта', icon: IcOutlineFolderOpen },
        ],
    },
    {
        title: 'Настройки',
        items: [
            { href: '#', label: 'Настройки', icon: IcOutlineSettings },
        ],
    },
]

export function MainNav() {
    return (
        <div className="space-y-2">
            {menuData.map((group, groupIndex) => (
                <div key={group.title}>
                    <SidebarGroup>
                        <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
                        <SidebarMenu>
                            {group.items.map((item) => {
                                const Icon = item.icon
                                return (
                                    <SidebarMenuLink
                                        key={`${item.href}`}
                                        href={item.href}
                                    >
                                        <Icon fontSize={SIDEBAR_ICON_SIZE} />
                                        <span>{item.label}</span>
                                    </SidebarMenuLink>
                                )
                            })}
                        </SidebarMenu>
                    </SidebarGroup>
                    {groupIndex < menuData.length - 1 && <Separator />}
                </div>
            ))}
        </div>
    )
}
