import { Slot } from '@radix-ui/react-slot'
import { Link } from '@tanstack/react-router'
import { cva, type VariantProps } from 'class-variance-authority'
import clsx from 'clsx'
import type { ReactNode } from 'react'

interface SidebarMenuButtonProps
    extends React.ComponentProps<'button'>,
        VariantProps<typeof sidebarMenuButtonVariants> {
    asChild?: boolean
    href?: string
    isActive?: boolean
}

interface SidebarLinkProps {
    href: string
    children: ReactNode
}

export function SidebarMenu({ className, children, ...props }: React.ComponentProps<'div'>) {
    return (
        <div
            className={clsx('flex w-full min-w-0 flex-col gap-1', className)}
            {...props}
        >
            {children}
        </div>
    )
}

const sidebarMenuButtonVariants = cva(
    [
        'flex w-full items-center gap-2 overflow-hidden relative rounded-md py-3 px-6 text-left',
        'transition-colors duration-150 whitespace-nowrap font-medium',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-500 cursor-pointer',
        'group-data-[state=collapsed]:justify-center group-data-[state=collapsed]:px-2',
        'group-data-[state=collapsed]:[&>*:not(:first-child)]:hidden',
        '[&_svg]:shrink-0',
    ],
    {
        variants: {
            variant: {
                default:
                    'text-sidebar-primary-foreground hover:bg-sidebar-accent/25 [&_svg]:text-sidebar-accent',
                active: 'text-sidebar-primary-foreground-active bg-sidebar-accent',
                footer: 'text-sidebar-primary-foreground-active bg-sidebar-accent flex justify-center align-center',
            },

            size: {
                default: 'text-sm',
                lg: 'text-sm py-5 group-data-[state=collapsed]:py-3',
            },
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
        },
    },
)

export function SidebarMenuButton({
    children,
    href,
    asChild,
    isActive,
    variant,
    size,
    className,
    ...props
}: SidebarMenuButtonProps) {
    const finalVariants = isActive ? 'active' : variant

    const componentProps = {
        className: clsx(sidebarMenuButtonVariants({ variant: finalVariants, size }), className),
        ...(href ? { href } : null),
        ...props,
    }

    const Comp = asChild ? Slot : 'button'

    return <Comp {...componentProps}>{children}</Comp>
}

export function SidebarMenuLink({ href, children }: SidebarLinkProps) {
    return (
        <Link to={href}>
            {/* Коллбек принимающий isActive, isActive вычисляется
            автоматически в TanStack Router на основе pathname роута  */}
            {({ isActive }) => {
                return (
                    <SidebarMenuButton
                        href={href}
                        isActive={isActive}
                    >
                        {children}
                    </SidebarMenuButton>
                )
            }}
        </Link>
    )
}
