import clsx from 'clsx'

export function SidebarGroup({ className, children, ...props }: React.ComponentProps<'div'>) {
    return (
        <div
            className={clsx('relative flex w-full flex-col p-2', className)}
            {...props}
        >
            {children}
        </div>
    )
}

export function SidebarGroupLabel({ className, children, ...props }: React.ComponentProps<'div'>) {
    return (
        <div
            className={clsx(
                'flex items-center h-8 px-6 text-xs uppercase font-medium tracking-widest duration-100 ease-out text-sidebar-primary-foreground text-nowrap',
                'group-data-[state=collapsed]:hidden',
                className,
            )}
            {...props}
        >
            {children}
        </div>
    )
}
