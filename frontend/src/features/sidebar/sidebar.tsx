import clsx from 'clsx'
import IcOutlineSms from '~icons/ic/outline-sms'
import IcOutlineMenuOpen from '~icons/ic/outline-menu-open'

import { useSidebarStore } from '@/services/sidebar'

import { SidebarContent } from './ui/kit/sidebar-content'
import { SidebarFooter } from './ui/kit/sidebar-footer'
import { SidebarHeader } from './ui/kit/sidebar-header'

import { SidebarMenuButton } from './ui/kit/sidebar-menu'
import { SidebarLogo } from './ui/kit/sidebar-logo'
import { MainNav } from './main-nav'
import { cn } from '@/shared/lib/css'

export const SIDEBAR_ICON_SIZE = '1.25rem'

export function Sidebar() {
    const { isOpen, toggleOpen } = useSidebarStore()

    return (
        <aside
            className={clsx(
                `group shrink-0 h-screen sticky top-0 text-sidebar-primary-foreground ease-linear overflow-y-auto overflow-x-hidden bg-sidebar transition-[width] duration-100`,
                isOpen ? 'w-[var(--sidebar-width-expanded)]' : 'w-[var(--sidebar-width-collapsed)]',
            )}
            data-state={isOpen ? 'open' : 'collapsed'}
        >
            <div className="flex flex-col h-full w-full overflow-hidden">
                <SidebarHeader>
                    <SidebarLogo />
                    <button
                        type="button"
                        onClick={toggleOpen}
                        className="cursor-pointer"
                    >
                        <IcOutlineMenuOpen
                            fontSize={SIDEBAR_ICON_SIZE}
                            color="var(--toggle-gray)"
                            className={cn(!isOpen && '-scale-x-100')}
                        />
                    </button>
                </SidebarHeader>
                <SidebarContent>
                    <MainNav />
                </SidebarContent>
                <SidebarFooter className="pt-0">
                    <SidebarMenuButton
                        variant={'footer'}
                        size={'lg'}
                    >
                        <IcOutlineSms fontSize={SIDEBAR_ICON_SIZE} /> <span>Мессенджер</span>
                    </SidebarMenuButton>
                </SidebarFooter>
            </div>
        </aside>
    )
}
