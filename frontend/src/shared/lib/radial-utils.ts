export const TAU = 2 * Math.PI

/**
 * Ограничивает число в диапазоне [min, max].
 */
export function clamp(n: number, min: number, max: number) {
    return Math.min(max, Math.max(min, n))
}

/**
 * Конвертирует градусы в радианы.
 */
export function degToRad(deg: number) {
    return (deg * Math.PI) / 180
}

/**
 * Конвертирует угол в градусах в координаты (x, y) на окружности.
 */
export function angleToPoint(angleDeg: number, r: number) {
    const angleRad = degToRad(angleDeg - 90)
    return { x: r * Math.cos(angleRad), y: r * Math.sin(angleRad) }
}

/**
 * Вычисляет длину окружности для заданного радиуса.
 */
export function circumference(r: number) {
    return TAU * r
}
