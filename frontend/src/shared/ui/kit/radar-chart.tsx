'use client'
import { PolarAngleAxis, PolarGrid, PolarRadiusAxis, Radar, RadarChart } from 'recharts'
import {
    ChartContainer,
    ChartLegend,
    ChartLegendContent,
    ChartTooltip,
    ChartTooltipContent,
} from './chart'
import { cn } from '@/shared/lib/css'

export const description = 'A radar chart showing team competencies vs required task competencies'

export type RadarChartData = Record<string, string | number>

export type RadarChartConfig = Record<
    string,
    {
        label: string
        color: string
    }
>

interface RadarChartCompetenciesProps {
    data?: RadarChartData[]
    config?: RadarChartConfig
    className?: string
    angleKey?: string
    maxValue?: number
    showLegend?: boolean
    showTooltip?: boolean
    showGrid?: boolean
    showRadiusAxis?: boolean
}

export function RadarChartCompetencies({
    data,
    config,
    className,
    angleKey = 'skill',
    maxValue = 5,
    showLegend = true,
    showTooltip = true,
    showGrid = true,
    showRadiusAxis = true,
}: RadarChartCompetenciesProps) {
    if (!data || !config) return null

    return (
        <div className="flex flex-col gap-3">
            <div className="relative">
                <ChartContainer
                    config={config}
                    className={cn('mx-auto w-full min-h-[400px]', className)}
                >
                    <RadarChart
                        data={data}
                        margin={{ top: 24, right: 24, bottom: 24, left: 24 }}
                    >
                        {showTooltip && (
                            <ChartTooltip
                                cursor={false}
                                content={<ChartTooltipContent />}
                            />
                        )}
                        <PolarAngleAxis
                            dataKey={angleKey}
                            tickSize={20}
                            tick={{
                                fontSize: 11,
                                fill: '#3E4045',
                                fontFamily: 'Onest, sans-serif',
                                fontWeight: 500,
                            }}
                            className="text-xs"
                        />
                        {showGrid && (
                            <PolarGrid
                                stroke="#969FB6"
                                gridType="circle"
                                strokeOpacity={0.3}
                            />
                        )}
                        {showRadiusAxis && (
                            <PolarRadiusAxis
                                angle={90}
                                domain={[0, maxValue]}
                                tick={{
                                    fontSize: 10,
                                    fill: '#969FB6',
                                    fontFamily: 'Onest, sans-serif',
                                }}
                                tickCount={maxValue + 1}
                            />
                        )}
                        {Object.entries(config).map(([key, configItem]) => (
                            <Radar
                                key={key}
                                dataKey={key}
                                stroke={configItem.color}
                                fill={configItem.color}
                                fillOpacity={0.1}
                                strokeWidth={2}
                                isAnimationActive={false}
                                dot={{
                                    r: 3,
                                    fill: configItem.color,
                                    strokeWidth: 0,
                                }}
                            />
                        ))}
                        {showLegend && <ChartLegend content={<ChartLegendContent />} />}
                    </RadarChart>
                </ChartContainer>
            </div>
        </div>
    )
}

// Keep the old component for backward compatibility
export function ChartRadarDots() {
    return <RadarChartCompetencies />
}
