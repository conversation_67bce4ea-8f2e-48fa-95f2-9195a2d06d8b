'use client'
import { PolarAngleAxis, PolarGrid, PolarRadiusAxis, Radar, RadarChart } from 'recharts'
import { Card, CardContent } from './card'
import {
    ChartContainer,
    ChartLegend,
    ChartLegendContent,
    ChartTooltip,
    ChartTooltipContent,
} from './chart'

export const description = 'A radar chart showing team competencies vs required task competencies'

// Sample data matching the Figma design
const defaultChartData = [
    {
        skill: 'Мультизадачность',
        teamCompetencies: 4,
        requiredCompetencies: 3,
    },
    {
        skill: 'Веб-дизайн',
        teamCompetencies: 3,
        requiredCompetencies: 5,
    },
    {
        skill: 'База данных',
        teamCompetencies: 5,
        requiredCompetencies: 4,
    },
    {
        skill: 'Бизнес',
        teamCompetencies: 2,
        requiredCompetencies: 3,
    },
    {
        skill: 'Программирование',
        teamCompetencies: 5,
        requiredCompetencies: 4,
    },
    {
        skill: 'Продажи и маркетинг',
        teamCompetencies: 2,
        requiredCompetencies: 4,
    }, {
        skill: 'Продажи и маркетинг',
        teamCompetencies: 2,
        requiredCompetencies: 4,
    },
]

const defaultChartConfig = {
    teamCompetencies: {
        label: 'Компетенции команды',
        color: '#62C498', // accent/accent_green100 from Figma
    },
    requiredCompetencies: {
        label: 'Требуемые компетенции задачи',
        color: '#969FB6', // neutral/neutral_gray_icon from Figma
    },
}

interface RadarChartCompetenciesProps {
    data?: typeof defaultChartData
    config?: typeof defaultChartConfig
    className?: string
}

export function RadarChartCompetencies({
    data = defaultChartData,
    config = defaultChartConfig,
    className,
}: RadarChartCompetenciesProps) {
    return (
        <Card className={className}>
            <CardContent className="p-6">
                <div className="flex flex-col gap-3">
                    <div className="relative">
                        <ChartContainer
                            config={config}
                            className="mx-auto aspect-square w-full min-w-[800px] min-h-[290px] opacity-80"
                        >
                            <RadarChart
                                data={data}
                                width={400}
                                height={290}
                                margin={{ top: 24, right: 24, bottom: 24, left: 24 }}
                            >
                                <ChartTooltip
                                    cursor={false}
                                    content={<ChartTooltipContent />}
                                />
                                <PolarAngleAxis
                                    dataKey="skill"
                                    tick={{
                                        fontSize: 11,
                                        fill: '#3E4045', // neutral/neutral_black100 from Figma
                                        fontFamily: 'Onest, sans-serif',
                                        fontWeight: 500,
                                    }}
                                    className="text-xs"
                                />
                                <PolarGrid
                                    stroke="#969FB6"
                                    strokeOpacity={0.3}
                                />
                                <PolarRadiusAxis
                                    angle={90}
                                    domain={[0, 5]}
                                    tick={{
                                        fontSize: 10,
                                        fill: '#969FB6',
                                        fontFamily: 'Onest, sans-serif',
                                    }}
                                    tickCount={6}
                                />
                                <Radar
                                    dataKey="teamCompetencies"
                                    stroke={config.teamCompetencies.color}
                                    fill={config.teamCompetencies.color}
                                    fillOpacity={0.1}
                                    strokeWidth={2}
                                    dot={{
                                        r: 3,
                                        fill: config.teamCompetencies.color,
                                        strokeWidth: 0,
                                    }}
                                />
                                <Radar
                                    dataKey="requiredCompetencies"
                                    stroke={config.requiredCompetencies.color}
                                    fill={config.requiredCompetencies.color}
                                    fillOpacity={0.1}
                                    strokeWidth={2}
                                    dot={{
                                        r: 3,
                                        fill: config.requiredCompetencies.color,
                                        strokeWidth: 0,
                                    }}
                                />
                                <ChartLegend content={<ChartLegendContent />} />
                            </RadarChart>
                        </ChartContainer>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}

// Keep the old component for backward compatibility
export function ChartRadarDots() {
    return <RadarChartCompetencies />
}
