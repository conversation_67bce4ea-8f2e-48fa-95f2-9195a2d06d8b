import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { RadarChartCompetencies, ChartRadarDots } from './radar-chart'

describe('RadarChartCompetencies', () => {
    it('renders without crashing', () => {
        render(<RadarChartCompetencies />)
        // Check if the chart container is rendered
        expect(document.querySelector('[data-slot="chart"]')).toBeInTheDocument()
    })

    it('renders with custom data', () => {
        const customData = [
            { skill: 'Test Skill', teamCompetencies: 3, requiredCompetencies: 4 },
        ]
        render(<RadarChartCompetencies data={customData} />)
        expect(document.querySelector('[data-slot="chart"]')).toBeInTheDocument()
    })

    it('renders legend with correct labels', () => {
        render(<RadarChartCompetencies />)
        expect(screen.getByText('Компетенции команды')).toBeInTheDocument()
        expect(screen.getByText('Требуемые компетенции задачи')).toBeInTheDocument()
    })

    it('applies custom className', () => {
        const { container } = render(<RadarChartCompetencies className="custom-class" />)
        expect(container.firstChild).toHaveClass('custom-class')
    })
})

describe('ChartRadarDots (Legacy)', () => {
    it('renders without crashing', () => {
        render(<ChartRadarDots />)
        expect(document.querySelector('[data-slot="chart"]')).toBeInTheDocument()
    })
})
