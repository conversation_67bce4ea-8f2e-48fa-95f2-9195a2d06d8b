import type { Meta, StoryObj } from '@storybook/react-vite'
import { ru } from 'react-day-picker/locale'

import { Calendar } from './calendar'

const meta = {
    component: Calendar,
} satisfies Meta<typeof Calendar>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        mode: 'single',
        captionLayout: 'label',
        showOutsideDays: true,
        locale: ru,
    },
}
