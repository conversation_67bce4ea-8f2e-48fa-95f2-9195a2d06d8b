import type { Meta, StoryObj } from '@storybook/react-vite'
import IcOutlineSearch from '~icons/ic/outline-search'

import { Input } from './input'

const meta = {
    component: Input,
} satisfies Meta<typeof Input>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        defaultValue: '<EMAIL>',
        type: 'email',
    },
}

export const Placeholder: Story = {
    args: {
        placeholder: 'Введите электронную почту',
        type: 'email',
    },
}

export const Disabled: Story = {
    args: {
        placeholder: 'Введите электронную почту',
        type: 'email',
        disabled: true,
    },
}

export const WithStartIcon: Story = {
    args: {
        placeholder: 'Поиск...',
        StartIcon: IcOutlineSearch,
    },
}
