'use client'

import * as React from 'react'
import * as ToggleGroupPrimitive from '@radix-ui/react-toggle-group'
import { type VariantProps } from 'class-variance-authority'

import { cn } from '@/shared/lib/css'
import { toggleVariants } from '@/shared/ui/kit/toggle'

const ToggleGroupContext = React.createContext<VariantProps<typeof toggleVariants>>({
    size: 'default',
    variant: 'default',
})

function ToggleGroup({
    className,
    size,
    variant,
    children,
    ...props
}: React.ComponentProps<typeof ToggleGroupPrimitive.Root> & VariantProps<typeof toggleVariants>) {
    const contextValue = React.useMemo(() => ({ variant, size }), [variant, size])

    return (
        <ToggleGroupPrimitive.Root
            data-slot="toggle-group"
            data-variant={variant}
            data-size={size}
            className={cn(
                'group/toggle-group bg-white flex w-fit items-center rounded-[40px] data-[variant=outline]:shadow-sm',
                className,
            )}
            {...props}
        >
            <ToggleGroupContext value={contextValue}>{children}</ToggleGroupContext>
        </ToggleGroupPrimitive.Root>
    )
}

function ToggleGroupItem({
    className,
    children,
    variant,
    size,
    ...props
}: React.ComponentProps<typeof ToggleGroupPrimitive.Item> & VariantProps<typeof toggleVariants>) {
    const context = React.use(ToggleGroupContext)

    return (
        <ToggleGroupPrimitive.Item
            data-slot="toggle-group-item"
            data-variant={context.variant ?? variant}
            data-size={context.size ?? size}
            className={cn(
                toggleVariants({
                    variant: context.variant ?? variant,
                    size: context.size ?? size,
                }),
                'min-w-0 flex-1 shrink-0 shadow-none focus:z-10 focus-visible:z-10',
                className,
            )}
            {...props}
        >
            {children}
        </ToggleGroupPrimitive.Item>
    )
}

export { ToggleGroup, ToggleGroupItem }
