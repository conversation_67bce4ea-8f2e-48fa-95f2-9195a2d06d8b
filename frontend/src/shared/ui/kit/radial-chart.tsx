import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/shared/lib/css'
import { circumference, clamp } from '@/shared/lib/radial-utils'

const radialChartVariants = cva('relative inline-flex items-center justify-center', {
    variants: {
        size: {
            default: 'text-2xl',
        },
    },
    defaultVariants: {
        size: 'default',
    },
})

interface RadialChartProps extends VariantProps<typeof radialChartVariants> {
    value: number
    className?: string
    primaryColor?: string
    overflowColor?: string
    backgroundColor?: string
    labelColor?: string
    labelColorOverflow?: string
    showLabel?: boolean
}

function RadialChart({
    className,
    value,
    primaryColor = '#62C498',
    overflowColor = '#FF7D7F',
    backgroundColor = '#E5E7EB',
    labelColor = 'black',
    labelColorOverflow = '#FF7D7F',
    size = 'default',
    showLabel = true,
}: RadialChartProps) {
    const sizeMap = {
        default: { radius: 50, strokeWidth: 8 },
    }

    const { radius, strokeWidth } = sizeMap[size ?? 'default']
    const innerRadius = radius
    const outerRadius = radius + strokeWidth / 1.5

    const overflowRadius = outerRadius + strokeWidth
    const svgSize = overflowRadius * 2

    const baseValue = Math.min(value, 100)
    const overflowValue = Math.max(0, value - 100)

    const basePercentage = baseValue / 100
    const overflowPercentage = overflowValue / 100

    const innerCirc = circumference(innerRadius)
    const outerCirc = circumference(outerRadius)

    // Вычисляем сдвиг для обводки. Это та часть окружности которую нужно скрыть.
    const innerDashOffset = dashOffsetFromPercent(basePercentage, innerRadius)
    const outerDashOffset = dashOffsetFromPercent(overflowPercentage, outerRadius)

    return (
        <div
            className={cn(radialChartVariants({ size, className }))}
            style={{ width: `${svgSize}px`, height: `${svgSize}px` }}
        >
            <svg
                width={svgSize}
                height={svgSize}
                viewBox={`0 0 ${svgSize} ${svgSize}`}
                className="absolute"
            >
                {/* Сдвигаем всю систему координат, чтобы наш компонент оказался в центре. */}
                <g transform={`translate(${overflowRadius}, ${overflowRadius}) rotate(-90)`}>
                    {/* Фоновый круг (внутренний) */}
                    <circle
                        cx={0}
                        cy={0}
                        r={innerRadius}
                        fill="none"
                        stroke={backgroundColor}
                        strokeWidth={strokeWidth}
                    />

                    {/* Основная дуга прогресса (до 100%) */}
                    {basePercentage > 0 && (
                        <circle
                            cx={0}
                            cy={0}
                            r={innerRadius}
                            fill="none"
                            stroke={primaryColor}
                            strokeWidth={strokeWidth}
                            strokeLinecap="round"
                            strokeDasharray={innerCirc}
                            strokeDashoffset={innerDashOffset}
                            style={{ transition: 'stroke-dashoffset 200ms ease-out' }}
                        />
                    )}

                    {/* Дуга переполнения (свыше 100%) */}
                    {overflowValue > 0 && (
                        <circle
                            cx={0}
                            cy={0}
                            r={outerRadius}
                            fill="none"
                            stroke={overflowColor}
                            strokeWidth={strokeWidth}
                            strokeLinecap="round"
                            strokeDasharray={outerCirc}
                            strokeDashoffset={outerDashOffset}
                            style={{ transition: 'stroke-dashoffset 500ms ease-out' }}
                        />
                    )}
                </g>
            </svg>

            {/* Центральный лейбл */}
            {showLabel && (
                <div
                    className="absolute text-center"
                    style={{
                        color: overflowValue > 0 ? labelColorOverflow : labelColor,
                        left: '50%',
                        top: '50%',
                        transform: 'translate(-50%, -50%)',
                    }}
                >
                    {Math.round(value)}%
                </div>
            )}
        </div>
    )
}

/**
 * Конвертирует прогресс в значение для обводки окружности.
 * Чтобы показать 25%, нужно скрыть 75%.
 */
export function dashOffsetFromPercent(percent: number, r: number) {
    const p = clamp(percent, 0, 1)
    return circumference(r) * (1 - p)
}

export { RadialChart, radialChartVariants }
export type { RadialChartProps }
