import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'
import { Gauge } from './gauge'

/**
 * # (Speedometer)
 *
 * ## Факты
 *
 * - **Динамический размер**: Компонент автоматически рассчитывает свой размер, чтобы идеально вместить gauge с любыми углами.
 * - **Настраиваемые углы**: Можно задать любые `startAngle` и `endAngle` для создания дуг разной формы.
 * - **Адаптивный лейбл**: Лейбл позиционируется в зависимости от текущего значения.
 * - **Анимации**: Стрелка анимируется с помощью transition-transform duration-300.
 *
 * ## Система углов
 *
 *   Система координат для углов похожа на циферблат часов:
 * - 0° — верх (12 часов)
 * - -90° — лево (9 часов)
 * - 90° — право (3 часа)
 * - 180° / -180° — низ (6 часов)
 */
const meta: Meta<typeof Gauge> = {
    component: Gauge,
    parameters: {
        layout: 'centered',
    },
    argTypes: {
        value: { control: 'range', description: 'Текущее значение' },
        startAngle: {
            control: { type: 'range', min: -180, max: 180 },
            description: 'Начальный угол',
        },
        endAngle: { control: { type: 'range', min: -180, max: 180 }, description: 'Конечный угол' },
        progressColor: { control: 'color', description: 'Цвет прогресса' },
        inactiveColor: { control: 'color', description: 'Цвет фона' },
        elementsColor: { control: 'color', description: 'Цвет стрелки и лейбла' },
        showLabel: { control: 'boolean', description: 'Показывать лейбл' },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        value: 75,
        startAngle: -90,
        endAngle: 90,
        elementsColor: 'black',
    },
}

export const WideArc: Story = {
    args: {
        value: 60,
        startAngle: -120,
        endAngle: 120,
        progressColor: '#8B5CF6',
    },
}
