import { Toaster as Sonner, type ToasterProps } from 'sonner'

const Toaster = ({ ...props }: ToasterProps) => {
    return (
        <Sonner
            className="backdrop-blur-3xl!"
            duration={3000}
            toastOptions={{
                classNames: {
                    toast: 'shadow-2xl!',
                    closeButton: '',
                },
            }}
            style={
                {
                    '--normal-bg': 'var(--popover)',
                    '--normal-text': 'var(--popover-foreground)',
                    '--normal-border': 'var(--border)',
                } as React.CSSProperties
            }
            position="top-center"
            {...props}
        />
    )
}

export { Toaster }
