import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'

import { Toaster } from './sonner'
import { But<PERSON> } from './button'
import { toast } from 'sonner'

const meta = {
    component: Toaster,
} satisfies Meta<typeof Toaster>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {},
    render: () => (
        <>
            <Button onClick={() => toast.success('Hello world')}>Send toast</Button>
            <Toaster />
        </>
    ),
}
