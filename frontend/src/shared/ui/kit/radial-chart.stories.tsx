import type { Meta, StoryObj } from '@storybook/react-vite'
import { Radial<PERSON><PERSON> } from './radial-chart'

/**
 * # Radial Chart
 *
 * ## Факты
 *
 * - **Система оверфлоу**: Внутреннее кольцо показывает 0-100%, внеш
нее — переполнение свыше 100%.
 * - **Настраиваемые цвета**: Отдельные цвета для основного кольца, переполнения, фона и лейбла.
 *
 *
 * ## Система координат
 *
 * - SVG повернут на -90°, поэтому 0% начинается сверху (12 часов)
 * - Анимация идет по часовой стрелке
 * - Центральная точка (0,0) находится в середине компонента
 */
const meta: Meta<typeof RadialChart> = {
    component: RadialChart,
    parameters: {
        layout: 'centered',
    },
    argTypes: {
        value: {
            control: { type: 'range', min: 0, max: 200, step: 5 },
            description: 'Значение может превышать 100',
        },
        primaryColor: {
            control: 'color',
            description: 'Цвет основного прогресса',
        },
        overflowColor: {
            control: 'color',
            description: 'Цвет кольца оверфлоу',
        },
        backgroundColor: {
            control: 'color',
            description: 'Цвет фона',
        },
        labelColor: {
            control: 'color',
            description: 'Цвет лейбла',
        },
        labelColorOverflow: {
            control: 'color',
            description: 'Цвет лейбла при оверфлоу',
        },
        showLabel: {
            control: 'boolean',
            description: 'Показывать лейбл',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        value: 75,
    },
}

export const Complete: Story = {
    args: {
        value: 100,
    },
}

export const WithOverflow: Story = {
    args: {
        value: 125,
    },
}
