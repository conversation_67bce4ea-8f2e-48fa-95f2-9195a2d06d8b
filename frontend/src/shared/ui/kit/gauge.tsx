import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/shared/lib/css'
import { useMemo } from 'react'
import { angleToPoint, clamp } from '@/shared/lib/radial-utils'

const LABEL_OFFSET = 15
const MAX_LABEL_OFFSET = 30

const gaugeVariants = cva('relative inline-flex items-center justify-center', {
    variants: {
        size: {
            default: 'text-sm',
        },
    },
    defaultVariants: {
        size: 'default',
    },
})

interface GaugeProps extends VariantProps<typeof gaugeVariants> {
    value: number
    className?: string
    progressColor?: string
    inactiveColor?: string
    elementsColor?: string
    showLabel?: boolean
    startAngle?: number
    endAngle?: number
    valueMin?: number
    valueMax?: number
}

function Gauge({
    className,
    value,
    progressColor = '#FF7D7F',
    inactiveColor = '#E5E7EB',
    elementsColor = '#FF7D7F',
    size,
    showLabel = true,
    startAngle = -90,
    endAngle = 90,
    valueMin = 0,
    valueMax = 100,
}: GaugeProps) {
    const sizeMap = {
        default: { radius: 55, strokeWidth: 8, needleLength: 40 },
    }
    const { radius, strokeWidth, needleLength } = sizeMap[size ?? 'default']

    const { width, height, centerX, centerY, startPoint, endPoint } = useGaugeLayout(
        startAngle,
        endAngle,
        radius,
        strokeWidth,
    )

    const clampedValue = clamp(value, valueMin, valueMax)
    const percentage = (clampedValue - valueMin) / (valueMax - valueMin)

    // Переводим percentage в valueAngle (угол где находится value) и рассчитываем точку на дуге (valuePoint).
    const totalAngleSpan = endAngle - startAngle
    const valueAngle = startAngle + percentage * totalAngleSpan
    const valuePoint = angleToPoint(valueAngle, radius)

    // Флаги для SVG, определяющие, как рисовать дугу.
    const backgroundLargeArc = Math.abs(totalAngleSpan) > 180 ? 1 : 0 // 1, если дуга > 180°.
    const valueLargeArc = Math.abs(percentage * totalAngleSpan) > 180 ? 1 : 0
    const sweepFlag = totalAngleSpan > 0 ? 1 : 0 // 1, если рисуем по часовой стрелке.

    // Адаптивная позиция метки
    const labelPoint = calcLabelPosition(
        LABEL_OFFSET,
        MAX_LABEL_OFFSET,
        valueAngle,
        strokeWidth,
        radius,
    )

    return (
        <div
            className={cn(gaugeVariants({ size, className }))}
            style={{ width: `${width}px`, height: `${height}px` }}
        >
            <svg
                width={width}
                height={height}
                viewBox={`0 0 ${width} ${height}`}
                className="absolute"
            >
                {/* Сдвигаем всю систему координат, чтобы наш компонент оказался в центре. */}
                <g transform={`translate(${centerX}, ${centerY})`}>
                    {/* Фоновая дуга */}
                    <path
                        d={`M ${startPoint.x} ${startPoint.y} A ${radius} ${radius} 0 ${backgroundLargeArc} ${sweepFlag} ${endPoint.x} ${endPoint.y}`}
                        fill="none"
                        stroke={inactiveColor}
                        strokeWidth={strokeWidth}
                        strokeLinecap="round"
                    />

                    {/* Дуга с прогрессом */}
                    {percentage > 0 && (
                        <path
                            d={`M ${startPoint.x} ${startPoint.y} A ${radius} ${radius} 0 ${valueLargeArc} ${sweepFlag} ${valuePoint.x} ${valuePoint.y}`}
                            fill="none"
                            stroke={progressColor}
                            strokeWidth={strokeWidth}
                            strokeLinecap="round"
                        />
                    )}

                    {/* Стрелка */}
                    <g>
                        <line
                            x1="0"
                            y1="0"
                            x2={needleLength}
                            y2="0"
                            stroke={elementsColor}
                            strokeWidth="2"
                            strokeLinecap="round"
                            transform={`rotate(${valueAngle - 90})`}
                            className="transition-transform duration-300 ease-out"
                        />
                    </g>
                </g>
            </svg>

            {/* Лейбл */}
            {showLabel && (
                <div
                    className={cn('absolute font-medium')}
                    style={{
                        color: elementsColor,
                        left: labelPoint.x + centerX,
                        top: labelPoint.y + centerY,
                        transform: 'translate(-50%, -50%)',
                    }}
                >
                    {Math.round(clampedValue) + '%'}
                </div>
            )}
        </div>
    )
}

/**
 * Функция для адаптивного расчета позиции лейбла:
 * больше на краях, меньше в центре.
 */
const calcLabelPosition = (
    baseOffset: number,
    maxOffset: number,
    valueAngle: number,
    strokeWidth: number,
    radius: number,
) => {
    const angleFromCenter = Math.abs(valueAngle)
    const offsetMultiplier = Math.min(angleFromCenter / 90, 1)
    const adaptiveOffset = baseOffset + (maxOffset - baseOffset) * offsetMultiplier
    const labelDistance = radius + strokeWidth / 2 + adaptiveOffset
    const labelPoint = angleToPoint(valueAngle, labelDistance)

    return labelPoint
}

/**
 * Хук для расчета геометрии и размеров Gauge компонента.
 */
const useGaugeLayout = (
    startAngle: number,
    endAngle: number,
    radius: number,
    strokeWidth: number,
) => {
    return useMemo(() => {
        // 1. Собираем ключевые точки дуги, чтобы найти её границы.
        const startPoint = angleToPoint(startAngle, radius)
        const endPoint = angleToPoint(endAngle, radius)
        const points = [
            startPoint,
            endPoint,
            { x: 0, y: 0 },
        ]

        // Добавляем точки для точности.
        for (let angle = startAngle; angle <= endAngle; angle += 10) {
            points.push(angleToPoint(angle, radius))
        }

        // 2. Находим минимальные и максимальные координаты.
        const minX = Math.min(...points.map((p) => p.x))
        const maxX = Math.max(...points.map((p) => p.x))
        const minY = Math.min(...points.map((p) => p.y))
        const maxY = Math.max(...points.map((p) => p.y))

        // 3. Рассчитываем размеры и отступы.
        const padding = strokeWidth * 2 // Отступ, чтобы обводка не обрезалась.
        const width = maxX - minX + padding
        const height = maxY - minY + padding

        // 4. Рассчитываем новый центр для сдвига системы координат.
        // Это нужно, чтобы дуга, нарисованная вокруг (0,0), оказалась в центре нового SVG.
        const centerX = -minX + padding / 2
        const centerY = -minY + padding / 2

        return { width, height, centerX, centerY, startPoint, endPoint }
    }, [
        startAngle,
        endAngle,
        radius,
        strokeWidth,
    ])
}

export { Gauge, gaugeVariants }
export type { GaugeProps }
