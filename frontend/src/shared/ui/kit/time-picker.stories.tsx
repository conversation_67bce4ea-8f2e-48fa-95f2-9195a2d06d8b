import type { Meta, StoryObj } from '@storybook/react-vite'

import { TimePicker } from './time-picker'
import IcOutlineAccessTime from '~icons/ic/outline-access-time'

const meta = {
    component: TimePicker,
} satisfies Meta<typeof TimePicker>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        EndIcon: IcOutlineAccessTime,
        defaultValue: '10:30',
        className: 'w-[110px]',
    },
}
