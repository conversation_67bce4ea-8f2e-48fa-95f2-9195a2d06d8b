import type { Meta, StoryObj } from '@storybook/react-vite'

import { RadarChartCompetencies } from './radar-chart'

/**
 * # Radar Chart Competencies
 *
 * A radar chart component that displays team competencies vs required task competencies.
 * Based on the Figma design specification with two data series and a legend.
 *
 * ## Features
 * - **Two data series**: Team competencies and required task competencies
 * - **Six skill categories**: Мультизадачность, Веб-дизайн, База данных, Бизнес, Программирование, Продажи и маркетинг
 * - **Interactive legend**: Shows/hides data series
 * - **Customizable data**: Accept custom data and configuration
 * - **Responsive design**: Adapts to container size
 */
const meta: Meta<typeof RadarChartCompetencies> = {
    component: RadarChartCompetencies,
    parameters: {
        layout: 'centered',
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        data: [
            { skill: 'Веб-дизайн', current: 5, target: 4 },
            { skill: 'База данных', current: 3, target: 5 },
            { skill: 'Бизнес', current: 2, target: 3 },
            { skill: 'Программирование', current: 4, target: 4 },
            { skill: 'Продажи и маркетинг', current: 3, target: 4 },
            { skill: 'Мультизадачность', current: 4, target: 3 },
        ],
        config: {
            current: {
                label: 'Компетенции команды',
                color: 'var(--color-primary)',
            },
            target: {
                label: 'Требуемые компетенции задачи',
                color: 'var(--color-badge-green)',
            },
        },
    },
}

export const Odd: Story = {
    args: {
        data: [
            { skill: 'Leadership', junior: 2, senior: 4, expert: 5 },
            { skill: 'Technical', junior: 3, senior: 5, expert: 5 },
            { skill: 'Communication', junior: 4, senior: 4, expert: 5 },
            { skill: 'Problem Solving', junior: 3, senior: 5, expert: 5 },
            { skill: 'Creativity', junior: 4, senior: 4, expert: 4 },
            { skill: 'Teamwork', junior: 5, senior: 4, expert: 4 },
            { skill: 'Development', junior: 5, senior: 4, expert: 4 },
        ],
        config: {
            junior: {
                label: 'Junior Level',
                color: '#10B981',
            },
            senior: {
                label: 'Senior Level',
                color: '#F59E0B',
            },
            expert: {
                label: 'Expert Level',
                color: '#EF4444',
            },
        },
        maxValue: 5,
    },
}
