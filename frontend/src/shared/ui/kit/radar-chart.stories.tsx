import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'

import { RadarChartCompetencies, ChartRadarDots } from './radar-chart'

/**
 * # Radar Chart Competencies
 *
 * A radar chart component that displays team competencies vs required task competencies.
 * Based on the Figma design specification with two data series and a legend.
 *
 * ## Features
 * - **Two data series**: Team competencies and required task competencies
 * - **Six skill categories**: Мультизадачность, Веб-дизайн, База данных, Бизнес, Программирование, Продажи и маркетинг
 * - **Interactive legend**: Shows/hides data series
 * - **Customizable data**: Accept custom data and configuration
 * - **Responsive design**: Adapts to container size
 */
const meta: Meta<typeof RadarChartCompetencies> = {
    component: RadarChartCompetencies,
    parameters: {
        layout: 'centered',
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        className: 'min-w-[536px] min-h-[331px]',
    },
}

export const CustomData: Story = {
    args: {
        className: 'min-w-[536px] min-h-[331px]',
        data: [
            { skill: 'Frontend', teamCompetencies: 5, requiredCompetencies: 4 },
            { skill: 'Backend', teamCompetencies: 3, requiredCompetencies: 5 },
            { skill: 'DevOps', teamCompetencies: 2, requiredCompetencies: 3 },
            { skill: 'Design', teamCompetencies: 4, requiredCompetencies: 4 },
            { skill: 'Testing', teamCompetencies: 3, requiredCompetencies: 4 },
            { skill: 'Management', teamCompetencies: 4, requiredCompetencies: 3 },
        ],
    },
}

export const HighPerformanceTeam: Story = {
    args: {
        data: [
            { skill: 'Мультизадачность', teamCompetencies: 5, requiredCompetencies: 3 },
            { skill: 'Веб-дизайн', teamCompetencies: 5, requiredCompetencies: 4 },
            { skill: 'База данных', teamCompetencies: 5, requiredCompetencies: 4 },
            { skill: 'Бизнес', teamCompetencies: 4, requiredCompetencies: 3 },
            { skill: 'Программирование', teamCompetencies: 5, requiredCompetencies: 5 },
            { skill: 'Продажи и маркетинг', teamCompetencies: 4, requiredCompetencies: 4 },
        ],
    },
}

export const SkillGapAnalysis: Story = {
    args: {
        data: [
            { skill: 'Мультизадачность', teamCompetencies: 2, requiredCompetencies: 4 },
            { skill: 'Веб-дизайн', teamCompetencies: 1, requiredCompetencies: 5 },
            { skill: 'База данных', teamCompetencies: 3, requiredCompetencies: 5 },
            { skill: 'Бизнес', teamCompetencies: 1, requiredCompetencies: 4 },
            { skill: 'Программирование', teamCompetencies: 2, requiredCompetencies: 5 },
            { skill: 'Продажи и маркетинг', teamCompetencies: 1, requiredCompetencies: 5 },
        ],
    },
}

export const CustomColors: Story = {
    args: {
        config: {
            teamCompetencies: {
                label: 'Current Skills',
                color: '#3B82F6', // Blue
            },
            requiredCompetencies: {
                label: 'Target Skills',
                color: '#EF4444', // Red
            },
        },
    },
}

// Legacy component for backward compatibility
export const LegacyRadarDots: StoryObj<Meta<typeof ChartRadarDots>> = {
    render: () => <ChartRadarDots />,
    parameters: {
        layout: 'centered',
    },
}
