import type { Meta, StoryObj } from '@storybook/react-vite'

import IcOutlineAdd from '~icons/ic/outline-add'
import IcOutlineWorkHistory from '~icons/ic/outline-work-history'
import IcOutlineWbSunny from '~icons/ic/outline-wb-sunny'
import IcRoundPhone from '~icons/ic/round-phone'
import IcBaselineDeleteOutline from '~icons/ic/baseline-delete-outline'

import { Button } from './button'

const meta = {
    component: Button,
} satisfies Meta<typeof Button>

export default meta

type Story = StoryObj<typeof meta>

export const Accent: Story = {
    name: 'Accent - профиль ПШЕ, задачи сотрудника',
    args: {
        children: [
            'Добавить задачу',
            <IcOutlineAdd key="icon" />,
        ],
        variant: 'accent',
        size: 'default',
    },
}

export const Secondary: Story = {
    name: 'Secondary - профиль ПШЕ, задачи сотрудника',
    args: {
        children: [
            'Отправить на обучение',
            <IcOutlineWorkHistory key="icon" />,
        ],
        variant: 'secondary',
        size: 'default',
    },
}

export const Default: Story = {
    name: 'Default - профиль ПШЕ, задачи сотрудника',
    args: {
        children: 'Назначить сверхурочные',
        variant: 'default',
        size: 'default',
    },
}

export const Primary: Story = {
    name: 'Primary - настройки, выбранная тема',
    args: {
        children: [
            <IcOutlineWbSunny key="icon" />,
            'Светлая',
        ],
        variant: 'primary',
        size: 'default',
    },
}

export const Destructive: Story = {
    name: 'Destructive - диалог входящего звонка',
    args: {
        children: <IcRoundPhone className="text-white rotate-135" />,
        variant: 'destructive',
        size: 'iconLg',
    },
}

export const Icon: Story = {
    name: 'Icon - профиль ПШЕ, таблица',
    args: {
        children: <IcBaselineDeleteOutline className="text-gray-500" />,
        className: 'size-8',
        variant: 'ghost',
        size: 'icon',
    },
}
