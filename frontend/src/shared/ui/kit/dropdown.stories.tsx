import type { Meta, StoryObj } from '@storybook/react-vite'

import { But<PERSON> } from './button'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from './dropdown'
import IcOutlineAdd from '~icons/ic/outline-add'

const meta = {
    component: DropdownMenu,
} satisfies Meta<typeof DropdownMenu>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    name: 'Default - профиль задачи',
    args: {
        children: [
            <DropdownMenuTrigger
                key="trigger"
                asChild
                className="w-[240px]"
            >
                <Button variant="accent">
                    Назначить сотрудника
                    <IcOutlineAdd key="icon" />
                </Button>
            </DropdownMenuTrigger>,
            <DropdownMenuContent
                key="content"
                side="bottom"
                className="w-[240px]"
            >
                <DropdownMenuItem>Фёдор Авдеев</DropdownMenuItem>
                <DropdownMenuItem>Фёдор Авдеев</DropdownMenuItem>
                <DropdownMenuItem>Фёдор Авдеев</DropdownMenuItem>
                <DropdownMenuItem>Фёдор Авдеев</DropdownMenuItem>
                <DropdownMenuItem>Фёдор Авдеев</DropdownMenuItem>
            </DropdownMenuContent>,
        ],
    },
}
