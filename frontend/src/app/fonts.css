/* onest-regular - cyrillic_latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 400;
    src: url('@/shared/assets/onest-v9-cyrillic_latin-regular.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}
/* onest-500 - cyrillic_latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 500;
    src: url('@/shared/assets/onest-v9-cyrillic_latin-500.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
}

html,
body,
* {
    font-family: 'Onest', sans-serif;
}
