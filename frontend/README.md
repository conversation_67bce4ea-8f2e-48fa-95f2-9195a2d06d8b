**Необходимые зависимости**

- node >= 22.16
- pnpm >= 10

```bash
npm install -g pnpm@latest-10
```

**Установка зависимостей**

```bash
pnpm i
```

**Запуск сервера разработки**

```bash
pnpm dev
```

**Продакшн билд**

```bash
pnpm build
```

**Предпросмотр продакшн билда локально (не для продакшена)**

```bash
pnpm preview
```

**Запуск storybook**

```bash
pnpm storybook
```

**Детали стека**

- Разработка компонентов ведется на основе [Shadcn](https://ui.shadcn.com/docs/cli) (Radix Primitives)
- Переиспользуемые компоненты документируются в [Storybook](https://storybook.js.org/docs/get-started/whats-a-story)
- File-based роутинг с помощью [Tanstack Router](https://tanstack.com/router/v1)
- Серверный стейт, кэширование и запросы с помощью [Tanstack Query](https://tanstack.com/query/v5)
- Глобальный стейт с помощью [Zustand](https://github.com/pmndrs/zustand)
- Стили указываются через [Tailwind](https://tailwindcss.com/)
- Библиотека иконок [Google Material Icons](https://material.io/resources/icons/), импорт с помощью Unplugin-Icons из [этого сета](https://icon-sets.iconify.design/ic/)
- В проекте настроены [ESLint](https://eslint.org/), [Prettier](https://prettier.io/), и pre-commit hook с ними
